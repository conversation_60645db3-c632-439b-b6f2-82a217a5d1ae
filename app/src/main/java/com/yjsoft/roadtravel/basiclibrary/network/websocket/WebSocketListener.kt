package com.yjsoft.roadtravel.basiclibrary.network.websocket

import okhttp3.Response
import okhttp3.WebSocket
import okio.ByteString

/**
 * WebSocket事件监听器接口
 * 定义WebSocket连接过程中的各种事件回调
 */
interface WebSocketListener {
    
    /**
     * 连接打开时调用
     * @param webSocket WebSocket实例
     * @param response 服务器响应
     */
    fun onOpen(webSocket: WebSocket, response: Response) {}
    
    /**
     * 接收到文本消息时调用
     * @param webSocket WebSocket实例
     * @param text 接收到的文本消息
     */
    fun onMessage(webSocket: WebSocket, text: String) {}
    
    /**
     * 接收到二进制消息时调用
     * @param webSocket WebSocket实例
     * @param bytes 接收到的二进制数据
     */
    fun onMessage(webSocket: WebSocket, bytes: ByteString) {}
    
    /**
     * 连接正在关闭时调用
     * @param webSocket WebSocket实例
     * @param code 关闭代码
     * @param reason 关闭原因
     */
    fun onClosing(webSocket: WebSocket, code: Int, reason: String) {}
    
    /**
     * 连接已关闭时调用
     * @param webSocket WebSocket实例
     * @param code 关闭代码
     * @param reason 关闭原因
     */
    fun onClosed(webSocket: WebSocket, code: Int, reason: String) {}
    
    /**
     * 连接失败时调用
     * @param webSocket WebSocket实例
     * @param throwable 异常信息
     * @param response 服务器响应（可能为null）
     */
    fun onFailure(webSocket: WebSocket, throwable: Throwable, response: Response?) {}
}

/**
 * 简化的WebSocket监听器
 * 提供更简洁的事件回调接口
 */
interface SimpleWebSocketListener {
    
    /**
     * 连接成功
     */
    fun onConnected() {}
    
    /**
     * 接收到消息
     * @param message 消息内容
     */
    fun onMessageReceived(message: String) {}
    
    /**
     * 连接断开
     * @param code 关闭代码
     * @param reason 关闭原因
     */
    fun onDisconnected(code: Int, reason: String) {}
    
    /**
     * 连接错误
     * @param error 错误信息
     */
    fun onError(error: Throwable) {}
    
    /**
     * 重连中
     * @param retryCount 重连次数
     */
    fun onReconnecting(retryCount: Int) {}
}

/**
 * WebSocket状态监听器
 * 专门用于监听连接状态变化
 */
interface WebSocketStateListener {
    
    /**
     * 状态变化时调用
     * @param oldState 旧状态
     * @param newState 新状态
     */
    fun onStateChanged(oldState: WebSocketState, newState: WebSocketState)
}

/**
 * WebSocket生命周期监听器
 * 用于监听WebSocket的生命周期事件
 */
interface WebSocketLifecycleListener {
    
    /**
     * WebSocket创建时调用
     */
    fun onCreate() {}
    
    /**
     * WebSocket开始连接时调用
     */
    fun onConnecting() {}
    
    /**
     * WebSocket连接成功时调用
     */
    fun onConnected() {}
    
    /**
     * WebSocket断开连接时调用
     */
    fun onDisconnected() {}
    
    /**
     * WebSocket销毁时调用
     */
    fun onDestroy() {}
}

/**
 * 组合监听器
 * 将多个监听器组合成一个
 */
class CompositeWebSocketListener(
    private val listeners: List<WebSocketListener>
) : WebSocketListener {
    
    override fun onOpen(webSocket: WebSocket, response: Response) {
        listeners.forEach { it.onOpen(webSocket, response) }
    }
    
    override fun onMessage(webSocket: WebSocket, text: String) {
        listeners.forEach { it.onMessage(webSocket, text) }
    }
    
    override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
        listeners.forEach { it.onMessage(webSocket, bytes) }
    }
    
    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        listeners.forEach { it.onClosing(webSocket, code, reason) }
    }
    
    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        listeners.forEach { it.onClosed(webSocket, code, reason) }
    }
    
    override fun onFailure(webSocket: WebSocket, throwable: Throwable, response: Response?) {
        listeners.forEach { it.onFailure(webSocket, throwable, response) }
    }
}

/**
 * 适配器模式：将SimpleWebSocketListener适配为WebSocketListener
 */
class SimpleWebSocketListenerAdapter(
    private val simpleListener: SimpleWebSocketListener
) : WebSocketListener {
    
    override fun onOpen(webSocket: WebSocket, response: Response) {
        simpleListener.onConnected()
    }
    
    override fun onMessage(webSocket: WebSocket, text: String) {
        simpleListener.onMessageReceived(text)
    }
    
    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        simpleListener.onDisconnected(code, reason)
    }
    
    override fun onFailure(webSocket: WebSocket, throwable: Throwable, response: Response?) {
        simpleListener.onError(throwable)
    }
}

/**
 * 抽象WebSocket监听器
 * 提供默认实现，子类只需重写感兴趣的方法
 */
abstract class AbstractWebSocketListener : WebSocketListener {
    
    override fun onOpen(webSocket: WebSocket, response: Response) {
        // 默认空实现
    }
    
    override fun onMessage(webSocket: WebSocket, text: String) {
        // 默认空实现
    }
    
    override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
        // 默认空实现
    }
    
    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        // 默认空实现
    }
    
    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        // 默认空实现
    }
    
    override fun onFailure(webSocket: WebSocket, throwable: Throwable, response: Response?) {
        // 默认空实现
    }
}

/**
 * WebSocket关闭代码常量
 */
object WebSocketCloseCodes {
    const val NORMAL_CLOSURE = 1000
    const val GOING_AWAY = 1001
    const val PROTOCOL_ERROR = 1002
    const val UNSUPPORTED_DATA = 1003
    const val NO_STATUS_RECEIVED = 1005
    const val ABNORMAL_CLOSURE = 1006
    const val INVALID_FRAME_PAYLOAD_DATA = 1007
    const val POLICY_VIOLATION = 1008
    const val MESSAGE_TOO_BIG = 1009
    const val MANDATORY_EXTENSION = 1010
    const val INTERNAL_SERVER_ERROR = 1011
    const val SERVICE_RESTART = 1012
    const val TRY_AGAIN_LATER = 1013
    const val BAD_GATEWAY = 1014
    const val TLS_HANDSHAKE = 1015
}
