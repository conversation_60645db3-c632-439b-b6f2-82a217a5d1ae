package com.yjsoft.roadtravel.basiclibrary.network.websocket

import java.util.concurrent.TimeUnit

/**
 * WebSocket配置类
 * 提供WebSocket连接的各种配置参数
 */
data class WebSocketConfig(
    /**
     * WebSocket服务器URL
     */
    val url: String,
    
    /**
     * 连接超时时间（毫秒）
     */
    val connectTimeout: Long = DEFAULT_CONNECT_TIMEOUT,
    
    /**
     * 读取超时时间（毫秒）
     */
    val readTimeout: Long = DEFAULT_READ_TIMEOUT,
    
    /**
     * 写入超时时间（毫秒）
     */
    val writeTimeout: Long = DEFAULT_WRITE_TIMEOUT,
    
    /**
     * 是否启用自动重连
     */
    val enableAutoReconnect: Boolean = true,
    
    /**
     * 重连策略配置
     */
    val reconnectConfig: ReconnectConfig = ReconnectConfig(),
    
    /**
     * 心跳配置
     */
    val heartbeatConfig: HeartbeatConfig = HeartbeatConfig(),
    
    /**
     * 自定义请求头
     */
    val headers: Map<String, String> = emptyMap(),
    
    /**
     * 是否启用日志
     */
    val enableLogging: Boolean = true,
    
    /**
     * 最大消息大小（字节）
     */
    val maxMessageSize: Long = DEFAULT_MAX_MESSAGE_SIZE
) {
    
    companion object {
        // 默认超时配置（毫秒）
        const val DEFAULT_CONNECT_TIMEOUT = 30_000L
        const val DEFAULT_READ_TIMEOUT = 30_000L
        const val DEFAULT_WRITE_TIMEOUT = 30_000L
        
        // 默认最大消息大小（10MB）
        const val DEFAULT_MAX_MESSAGE_SIZE = 10 * 1024 * 1024L
        
        /**
         * 创建默认配置
         */
        fun default(url: String): WebSocketConfig {
            return WebSocketConfig(url = url)
        }
        
        /**
         * 创建生产环境配置
         */
        fun production(url: String): WebSocketConfig {
            return WebSocketConfig(
                url = url,
                enableLogging = false,
                reconnectConfig = ReconnectConfig(
                    maxRetryCount = 5,
                    initialDelay = 1000L,
                    maxDelay = 30_000L
                ),
                heartbeatConfig = HeartbeatConfig(
                    enabled = true,
                    interval = 30_000L
                )
            )
        }
        
        /**
         * 创建开发环境配置
         */
        fun development(url: String): WebSocketConfig {
            return WebSocketConfig(
                url = url,
                enableLogging = true,
                reconnectConfig = ReconnectConfig(
                    maxRetryCount = 3,
                    initialDelay = 500L,
                    maxDelay = 10_000L
                ),
                heartbeatConfig = HeartbeatConfig(
                    enabled = true,
                    interval = 15_000L
                )
            )
        }
    }
}

/**
 * 重连策略配置
 */
data class ReconnectConfig(
    /**
     * 最大重连次数，-1表示无限重连
     */
    val maxRetryCount: Int = 3,
    
    /**
     * 初始重连延迟（毫秒）
     */
    val initialDelay: Long = 1000L,
    
    /**
     * 最大重连延迟（毫秒）
     */
    val maxDelay: Long = 30_000L,
    
    /**
     * 重连延迟倍数
     */
    val backoffMultiplier: Double = 2.0,
    
    /**
     * 重连延迟随机化因子（0.0-1.0）
     */
    val jitterFactor: Double = 0.1
) {
    
    /**
     * 计算下次重连延迟时间
     */
    fun calculateDelay(retryCount: Int): Long {
        val baseDelay = (initialDelay * Math.pow(backoffMultiplier, retryCount.toDouble())).toLong()
        val clampedDelay = minOf(baseDelay, maxDelay)
        
        // 添加随机化避免雷群效应
        val jitter = (clampedDelay * jitterFactor * Math.random()).toLong()
        return clampedDelay + jitter
    }
    
    /**
     * 检查是否应该继续重连
     */
    fun shouldRetry(retryCount: Int): Boolean {
        return maxRetryCount == -1 || retryCount < maxRetryCount
    }
}

/**
 * 心跳配置
 */
data class HeartbeatConfig(
    /**
     * 是否启用心跳
     */
    val enabled: Boolean = true,
    
    /**
     * 心跳间隔（毫秒）
     */
    val interval: Long = 30_000L,
    
    /**
     * 心跳超时时间（毫秒）
     */
    val timeout: Long = 10_000L,
    
    /**
     * 心跳消息内容
     */
    val message: String = "ping",
    
    /**
     * 心跳失败最大次数
     */
    val maxFailureCount: Int = 3
) {
    
    /**
     * 验证配置有效性
     */
    fun validate(): Boolean {
        return interval > 0 && timeout > 0 && timeout < interval && maxFailureCount > 0
    }
}

/**
 * WebSocket URL构建器
 */
class WebSocketUrlBuilder(private val baseUrl: String) {
    private val queryParams = mutableMapOf<String, String>()
    
    /**
     * 添加查询参数
     */
    fun addParam(key: String, value: String): WebSocketUrlBuilder {
        queryParams[key] = value
        return this
    }
    
    /**
     * 添加多个查询参数
     */
    fun addParams(params: Map<String, String>): WebSocketUrlBuilder {
        queryParams.putAll(params)
        return this
    }
    
    /**
     * 构建最终URL
     */
    fun build(): String {
        if (queryParams.isEmpty()) {
            return baseUrl
        }
        
        val separator = if (baseUrl.contains("?")) "&" else "?"
        val queryString = queryParams.entries.joinToString("&") { "${it.key}=${it.value}" }
        return "$baseUrl$separator$queryString"
    }
}
