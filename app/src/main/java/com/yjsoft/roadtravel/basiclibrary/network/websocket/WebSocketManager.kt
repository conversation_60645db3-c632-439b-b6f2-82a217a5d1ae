package com.yjsoft.roadtravel.basiclibrary.network.websocket

import android.content.Context
import com.yjsoft.roadtravel.basiclibrary.auth.TokenManager
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.SharedFlow
import okhttp3.OkHttpClient
import java.util.concurrent.ConcurrentHashMap

/**
 * WebSocket管理器
 * 提供WebSocket的统一管理入口，集成认证、错误处理等现有网络功能
 */
class WebSocketManager(
    private val context: Context,
    private val okHttpClient: OkHttpClient
) {

    companion object {
        private const val TAG = "WebSocketManager"

        // 使用WeakReference避免内存泄漏
        private var instanceRef: java.lang.ref.WeakReference<WebSocketManager>? = null
        private val instanceLock = Any()

        /**
         * 获取单例实例
         * 使用WeakReference避免内存泄漏警告
         */
        fun getInstance(context: Context, okHttpClient: OkHttpClient): WebSocketManager {
            synchronized(instanceLock) {
                // 检查现有实例是否仍然有效
                val existingInstance = instanceRef?.get()
                if (existingInstance != null) {
                    return existingInstance
                }

                // 创建新实例，使用ApplicationContext避免内存泄漏
                val newInstance = WebSocketManager(context.applicationContext, okHttpClient)
                instanceRef = java.lang.ref.WeakReference(newInstance)
                return newInstance
            }
        }

        /**
         * 清理实例引用
         */
        fun clearInstance() {
            synchronized(instanceLock) {
                instanceRef?.clear()
                instanceRef = null
            }
        }
    }
    
    // WebSocket客户端缓存
    private val clients = ConcurrentHashMap<String, WebSocketClient>()
    
    // 默认配置
    private var defaultConfig: WebSocketConfig? = null
    
    // 全局监听器
    private val globalListeners = mutableListOf<WebSocketListener>()
    
    /**
     * 设置默认配置
     */
    fun setDefaultConfig(config: WebSocketConfig) {
        this.defaultConfig = config
        log("设置默认WebSocket配置: ${config.url}")
    }
    
    /**
     * 创建WebSocket客户端
     * @param key 客户端标识符
     * @param config WebSocket配置，如果为null则使用默认配置
     * @return WebSocket客户端实例
     */
    fun createClient(key: String, config: WebSocketConfig? = null): WebSocketClient {
        val finalConfig = config ?: defaultConfig ?: throw IllegalStateException("未设置WebSocket配置")
        
        // 如果客户端已存在，先销毁旧的
        clients[key]?.destroy()
        
        // 创建新客户端
        val enhancedConfig = enhanceConfig(finalConfig)
        val client = WebSocketClient(enhancedConfig, okHttpClient)
        
        // 设置组合监听器
        val compositeListener = createCompositeListener()
        client.setListener(compositeListener)
        
        // 缓存客户端
        clients[key] = client
        
        log("创建WebSocket客户端: $key")
        return client
    }
    
    /**
     * 获取WebSocket客户端
     * @param key 客户端标识符
     * @return WebSocket客户端实例，如果不存在则返回null
     */
    fun getClient(key: String): WebSocketClient? {
        return clients[key]
    }
    
    /**
     * 移除WebSocket客户端
     * @param key 客户端标识符
     */
    fun removeClient(key: String) {
        clients[key]?.let { client ->
            client.destroy()
            clients.remove(key)
            log("移除WebSocket客户端: $key")
        }
    }
    
    /**
     * 获取所有客户端
     */
    fun getAllClients(): Map<String, WebSocketClient> {
        return clients.toMap()
    }
    
    /**
     * 连接指定客户端
     */
    fun connect(key: String) {
        clients[key]?.connect() ?: log("WebSocket客户端不存在: $key")
    }
    
    /**
     * 断开指定客户端
     */
    fun disconnect(key: String, code: Int = WebSocketCloseCodes.NORMAL_CLOSURE, reason: String = "主动断开") {
        clients[key]?.close(code, reason) ?: log("WebSocket客户端不存在: $key")
    }
    
    /**
     * 发送消息到指定客户端
     */
    fun sendMessage(key: String, message: String): Boolean {
        return clients[key]?.sendMessage(message) ?: run {
            log("WebSocket客户端不存在: $key")
            false
        }
    }
    
    /**
     * 获取指定客户端的消息流
     */
    fun getMessageFlow(key: String): SharedFlow<String>? {
        return clients[key]?.messageFlow
    }
    
    /**
     * 添加全局监听器
     */
    fun addGlobalListener(listener: WebSocketListener) {
        globalListeners.add(listener)
        log("添加全局WebSocket监听器")
    }
    
    /**
     * 移除全局监听器
     */
    fun removeGlobalListener(listener: WebSocketListener) {
        globalListeners.remove(listener)
        log("移除全局WebSocket监听器")
    }
    
    /**
     * 断开所有连接
     */
    fun disconnectAll() {
        log("断开所有WebSocket连接")
        clients.values.forEach { client ->
            client.close()
        }
    }
    
    /**
     * 销毁所有客户端
     */
    fun destroyAll() {
        log("销毁所有WebSocket客户端")
        clients.values.forEach { client ->
            client.destroy()
        }
        clients.clear()
        globalListeners.clear()
    }

    /**
     * 清理管理器资源
     * 应该在应用退出或不再需要WebSocket功能时调用
     */
    fun cleanup() {
        log("清理WebSocket管理器资源")
        destroyAll()
        // 清理静态实例引用
        clearInstance()
    }
    
    /**
     * 增强配置（添加认证等）
     */
    private fun enhanceConfig(config: WebSocketConfig): WebSocketConfig {
        val enhancedHeaders = config.headers.toMutableMap()
        
        // 添加认证头
        if (TokenManager.isInitialized()) {
            val token = TokenManager.getAccessToken()
            if (token.isNotEmpty()) {
                enhancedHeaders[NetworkConfig.HEADER_AUTHORIZATION] = token
            }
        }
        
        // 添加用户代理
        enhancedHeaders[NetworkConfig.HEADER_USER_AGENT] = buildUserAgent()
        
        return config.copy(headers = enhancedHeaders)
    }
    
    /**
     * 创建组合监听器
     */
    private fun createCompositeListener(): WebSocketListener {
        val listeners = mutableListOf<WebSocketListener>()
        
        // 添加全局监听器
        listeners.addAll(globalListeners)
        
        // 添加认证失败处理监听器
        listeners.add(AuthFailureListener())
        
        // 添加日志监听器
        listeners.add(LoggingListener())
        
        return CompositeWebSocketListener(listeners)
    }
    
    /**
     * 构建用户代理字符串
     */
    private fun buildUserAgent(): String {
        val packageInfo = try {
            context.packageManager.getPackageInfo(context.packageName, 0)
        } catch (e: Exception) {
            null
        }
        
        val appName = packageInfo?.applicationInfo?.loadLabel(context.packageManager) ?: "Unknown"
        val appVersion = packageInfo?.versionName ?: "Unknown"
        val androidVersion = android.os.Build.VERSION.RELEASE
        val deviceModel = "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL}"
        
        return "$appName/$appVersion (Android $androidVersion; $deviceModel) WebSocket/1.0"
    }
    
    /**
     * 认证失败监听器
     */
    private inner class AuthFailureListener : AbstractWebSocketListener() {
        override fun onFailure(webSocket: okhttp3.WebSocket, throwable: Throwable, response: okhttp3.Response?) {
            if (response?.code == 401) {
                log("WebSocket认证失败，清除Token")
                if (TokenManager.isInitialized()) {
                    // 在协程中调用suspend函数
                    CoroutineScope(Dispatchers.IO).launch {
                        try {
                            TokenManager.clearTokens()
                            log("Token清除成功")
                        } catch (e: Exception) {
                            log("Token清除失败: ${e.message}")
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 日志监听器
     */
    private inner class LoggingListener : AbstractWebSocketListener() {
        override fun onOpen(webSocket: okhttp3.WebSocket, response: okhttp3.Response) {
            log("WebSocket连接已建立")
        }
        
        override fun onClosed(webSocket: okhttp3.WebSocket, code: Int, reason: String) {
            log("WebSocket连接已关闭: code=$code, reason=$reason")
        }
        
        override fun onFailure(webSocket: okhttp3.WebSocket, throwable: Throwable, response: okhttp3.Response?) {
            log("WebSocket连接失败: ${throwable.message}")
        }
    }
    
    /**
     * 日志输出
     */
    private fun log(message: String) {
        LogManager.d(TAG, message)
    }
}
