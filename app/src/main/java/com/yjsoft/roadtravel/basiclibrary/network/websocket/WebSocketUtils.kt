package com.yjsoft.roadtravel.basiclibrary.network.websocket

import android.util.Log
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import java.net.URI
import java.net.URISyntaxException
import java.util.regex.Pattern

/**
 * WebSocket工具类
 * 提供WebSocket相关的工具方法，如URL验证、消息格式化等
 */
object WebSocketUtils {
    
    private const val TAG = "WebSocketUtils"
    
    // WebSocket URL正则表达式
    private val WS_URL_PATTERN = Pattern.compile(
        "^(ws|wss)://[a-zA-Z0-9.-]+(:[0-9]+)?(/.*)?$",
        Pattern.CASE_INSENSITIVE
    )
    
    /**
     * 验证WebSocket URL是否有效
     * @param url 要验证的URL
     * @return 是否有效
     */
    fun isValidWebSocketUrl(url: String?): Boolean {
        if (url.isNullOrBlank()) {
            return false
        }
        
        return try {
            // 使用正则表达式进行基本验证
            if (!WS_URL_PATTERN.matcher(url).matches()) {
                return false
            }
            
            // 使用URI进行更严格的验证
            val uri = URI(url)
            val scheme = uri.scheme?.lowercase()
            scheme == "ws" || scheme == "wss"
        } catch (e: URISyntaxException) {
            false
        }
    }
    
    /**
     * 将HTTP URL转换为WebSocket URL
     * @param httpUrl HTTP URL
     * @return WebSocket URL
     */
    fun httpToWebSocketUrl(httpUrl: String): String {
        return when {
            httpUrl.startsWith("https://") -> httpUrl.replace("https://", "wss://")
            httpUrl.startsWith("http://") -> httpUrl.replace("http://", "ws://")
            else -> httpUrl
        }
    }
    
    /**
     * 将WebSocket URL转换为HTTP URL
     * @param wsUrl WebSocket URL
     * @return HTTP URL
     */
    fun webSocketToHttpUrl(wsUrl: String): String {
        return when {
            wsUrl.startsWith("wss://") -> wsUrl.replace("wss://", "https://")
            wsUrl.startsWith("ws://") -> wsUrl.replace("ws://", "http://")
            else -> wsUrl
        }
    }
    
    /**
     * 格式化WebSocket消息为JSON
     * @param data 要格式化的数据
     * @return JSON字符串
     */
    fun formatMessageAsJson(data: Any): String {
        return try {
            Gson().toJson(data)
        } catch (e: Exception) {
            Log.w(TAG, "格式化消息为JSON失败: ${e.message}")
            data.toString()
        }
    }
    
    /**
     * 解析JSON消息
     * @param json JSON字符串
     * @param clazz 目标类型
     * @return 解析后的对象，失败时返回null
     */
    fun <T> parseJsonMessage(json: String, clazz: Class<T>): T? {
        return try {
            Gson().fromJson(json, clazz)
        } catch (e: JsonSyntaxException) {
            Log.w(TAG, "解析JSON消息失败: ${e.message}")
            null
        }
    }
    
    /**
     * 检查消息是否为JSON格式
     * @param message 消息内容
     * @return 是否为JSON格式
     */
    fun isJsonMessage(message: String): Boolean {
        if (message.isBlank()) {
            return false
        }
        
        val trimmed = message.trim()
        return (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                (trimmed.startsWith("[") && trimmed.endsWith("]"))
    }
    
    /**
     * 获取WebSocket关闭代码的描述
     * @param code 关闭代码
     * @return 描述文本
     */
    fun getCloseCodeDescription(code: Int): String {
        return when (code) {
            WebSocketCloseCodes.NORMAL_CLOSURE -> "正常关闭"
            WebSocketCloseCodes.GOING_AWAY -> "端点离开"
            WebSocketCloseCodes.PROTOCOL_ERROR -> "协议错误"
            WebSocketCloseCodes.UNSUPPORTED_DATA -> "不支持的数据类型"
            WebSocketCloseCodes.NO_STATUS_RECEIVED -> "未收到状态码"
            WebSocketCloseCodes.ABNORMAL_CLOSURE -> "异常关闭"
            WebSocketCloseCodes.INVALID_FRAME_PAYLOAD_DATA -> "无效的帧载荷数据"
            WebSocketCloseCodes.POLICY_VIOLATION -> "策略违规"
            WebSocketCloseCodes.MESSAGE_TOO_BIG -> "消息过大"
            WebSocketCloseCodes.MANDATORY_EXTENSION -> "强制扩展"
            WebSocketCloseCodes.INTERNAL_SERVER_ERROR -> "服务器内部错误"
            WebSocketCloseCodes.SERVICE_RESTART -> "服务重启"
            WebSocketCloseCodes.TRY_AGAIN_LATER -> "稍后重试"
            WebSocketCloseCodes.BAD_GATEWAY -> "网关错误"
            WebSocketCloseCodes.TLS_HANDSHAKE -> "TLS握手失败"
            else -> "未知错误码: $code"
        }
    }
    
    /**
     * 检查关闭代码是否表示正常关闭
     * @param code 关闭代码
     * @return 是否为正常关闭
     */
    fun isNormalClosure(code: Int): Boolean {
        return code == WebSocketCloseCodes.NORMAL_CLOSURE || code == WebSocketCloseCodes.GOING_AWAY
    }
    
    /**
     * 检查关闭代码是否应该重连
     * @param code 关闭代码
     * @return 是否应该重连
     */
    fun shouldReconnectOnClose(code: Int): Boolean {
        return when (code) {
            WebSocketCloseCodes.NORMAL_CLOSURE,
            WebSocketCloseCodes.GOING_AWAY,
            WebSocketCloseCodes.POLICY_VIOLATION -> false
            else -> true
        }
    }
    
    /**
     * 生成唯一的客户端ID
     * @param prefix 前缀
     * @return 唯一ID
     */
    fun generateClientId(prefix: String = "ws_client"): String {
        val timestamp = System.currentTimeMillis()
        val random = (Math.random() * 10000).toInt()
        return "${prefix}_${timestamp}_$random"
    }
    
    /**
     * 计算消息大小（字节）
     * @param message 消息内容
     * @return 字节大小
     */
    fun getMessageSize(message: String): Int {
        return message.toByteArray(Charsets.UTF_8).size
    }
    
    /**
     * 检查消息大小是否超过限制
     * @param message 消息内容
     * @param maxSize 最大大小（字节）
     * @return 是否超过限制
     */
    fun isMessageTooLarge(message: String, maxSize: Long): Boolean {
        return getMessageSize(message) > maxSize
    }
    
    /**
     * 截断过长的消息
     * @param message 原始消息
     * @param maxSize 最大大小（字节）
     * @return 截断后的消息
     */
    fun truncateMessage(message: String, maxSize: Long): String {
        if (!isMessageTooLarge(message, maxSize)) {
            return message
        }
        
        val bytes = message.toByteArray(Charsets.UTF_8)
        val truncatedBytes = bytes.copyOf(maxSize.toInt() - 3) // 预留3字节给"..."
        val truncatedString = String(truncatedBytes, Charsets.UTF_8)
        
        // 确保不会在多字节字符中间截断
        val lastValidIndex = truncatedString.lastIndexOf { char ->
            !char.isSurrogate()
        }
        
        return if (lastValidIndex > 0) {
            truncatedString.substring(0, lastValidIndex) + "..."
        } else {
            "..."
        }
    }
    
    /**
     * 格式化字节大小
     * @param bytes 字节数
     * @return 格式化后的字符串
     */
    fun formatByteSize(bytes: Long): String {
        return when {
            bytes < 1024 -> "${bytes}B"
            bytes < 1024 * 1024 -> "${bytes / 1024}KB"
            bytes < 1024 * 1024 * 1024 -> "${bytes / (1024 * 1024)}MB"
            else -> "${bytes / (1024 * 1024 * 1024)}GB"
        }
    }
    
    /**
     * 创建标准的WebSocket消息包装器
     * @param type 消息类型
     * @param data 消息数据
     * @param id 消息ID（可选）
     * @return 包装后的消息
     */
    fun createMessageWrapper(type: String, data: Any, id: String? = null): String {
        val wrapper = mutableMapOf<String, Any>()
        wrapper["type"] = type
        wrapper["data"] = data
        wrapper["timestamp"] = System.currentTimeMillis()
        
        id?.let { wrapper["id"] = it }
        
        return formatMessageAsJson(wrapper)
    }
    
    /**
     * 解析标准的WebSocket消息包装器
     * @param json JSON消息
     * @return 解析后的消息包装器，失败时返回null
     */
    fun parseMessageWrapper(json: String): MessageWrapper? {
        return parseJsonMessage(json, MessageWrapper::class.java)
    }
}

/**
 * 标准消息包装器
 */
data class MessageWrapper(
    val type: String,
    val data: Any,
    val timestamp: Long,
    val id: String? = null
)
