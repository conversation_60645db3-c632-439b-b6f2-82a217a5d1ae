package com.yjsoft.roadtravel.basiclibrary.network.websocket

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.CopyOnWriteArrayList

/**
 * WebSocket连接状态枚举
 */
enum class WebSocketState {
    /**
     * 未连接状态
     */
    DISCONNECTED,
    
    /**
     * 连接中状态
     */
    CONNECTING,
    
    /**
     * 已连接状态
     */
    CONNECTED,
    
    /**
     * 重连中状态
     */
    RECONNECTING,
    
    /**
     * 关闭中状态
     */
    CLOSING,
    
    /**
     * 已关闭状态
     */
    CLOSED,
    
    /**
     * 错误状态
     */
    ERROR;
    
    /**
     * 是否为连接状态
     */
    fun isConnected(): Boolean = this == CONNECTED
    
    /**
     * 是否为断开状态
     */
    fun isDisconnected(): Boolean = this == DISCONNECTED || this == CLOSED
    
    /**
     * 是否为连接中状态
     */
    fun isConnecting(): Boolean = this == CONNECTING || this == RECONNECTING
    
    /**
     * 是否为错误状态
     */
    fun isError(): Boolean = this == ERROR
    
    /**
     * 是否可以发送消息
     */
    fun canSendMessage(): Boolean = this == CONNECTED
    
    /**
     * 是否可以重连
     */
    fun canReconnect(): Boolean = this == DISCONNECTED || this == CLOSED || this == ERROR
}

/**
 * WebSocket状态管理器
 * 负责管理WebSocket的连接状态和状态变更通知
 */
class WebSocketStateManager {
    
    // 当前状态
    private val _currentState = MutableStateFlow(WebSocketState.DISCONNECTED)
    val currentState: StateFlow<WebSocketState> = _currentState.asStateFlow()
    
    // 状态监听器列表
    private val stateListeners = CopyOnWriteArrayList<WebSocketStateListener>()
    
    // 状态变更历史（用于调试）
    private val stateHistory = mutableListOf<StateChangeRecord>()
    private val maxHistorySize = 50
    
    /**
     * 获取当前状态
     */
    fun getCurrentState(): WebSocketState = _currentState.value
    
    /**
     * 更新状态
     * @param newState 新状态
     * @param reason 状态变更原因（可选）
     */
    fun updateState(newState: WebSocketState, reason: String? = null) {
        val oldState = _currentState.value
        if (oldState != newState) {
            _currentState.value = newState
            
            // 记录状态变更历史
            recordStateChange(oldState, newState, reason)
            
            // 通知监听器
            notifyStateListeners(oldState, newState)
        }
    }
    
    /**
     * 添加状态监听器
     */
    fun addStateListener(listener: WebSocketStateListener) {
        stateListeners.add(listener)
    }
    
    /**
     * 移除状态监听器
     */
    fun removeStateListener(listener: WebSocketStateListener) {
        stateListeners.remove(listener)
    }
    
    /**
     * 清除所有状态监听器
     */
    fun clearStateListeners() {
        stateListeners.clear()
    }
    
    /**
     * 获取状态变更历史
     */
    fun getStateHistory(): List<StateChangeRecord> = stateHistory.toList()
    
    /**
     * 清除状态历史
     */
    fun clearStateHistory() {
        stateHistory.clear()
    }
    
    /**
     * 重置状态管理器
     */
    fun reset() {
        updateState(WebSocketState.DISCONNECTED, "状态管理器重置")
        clearStateListeners()
        clearStateHistory()
    }
    
    /**
     * 记录状态变更
     */
    private fun recordStateChange(oldState: WebSocketState, newState: WebSocketState, reason: String?) {
        val record = StateChangeRecord(
            timestamp = System.currentTimeMillis(),
            fromState = oldState,
            toState = newState,
            reason = reason
        )
        
        stateHistory.add(record)
        
        // 限制历史记录大小
        if (stateHistory.size > maxHistorySize) {
            stateHistory.removeAt(0)
        }
    }
    
    /**
     * 通知状态监听器
     */
    private fun notifyStateListeners(oldState: WebSocketState, newState: WebSocketState) {
        stateListeners.forEach { listener ->
            try {
                listener.onStateChanged(oldState, newState)
            } catch (e: Exception) {
                // 忽略监听器异常，避免影响状态更新
                e.printStackTrace()
            }
        }
    }
}

/**
 * 状态变更记录
 */
data class StateChangeRecord(
    /**
     * 变更时间戳
     */
    val timestamp: Long,
    
    /**
     * 原状态
     */
    val fromState: WebSocketState,
    
    /**
     * 新状态
     */
    val toState: WebSocketState,
    
    /**
     * 变更原因
     */
    val reason: String? = null
) {
    
    /**
     * 格式化时间戳
     */
    fun getFormattedTime(): String {
        return java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", java.util.Locale.getDefault())
            .format(java.util.Date(timestamp))
    }
    
    /**
     * 转换为字符串
     */
    override fun toString(): String {
        val reasonText = reason?.let { " ($it)" } ?: ""
        return "${getFormattedTime()}: $fromState -> $toState$reasonText"
    }
}

/**
 * 状态验证器
 * 用于验证状态转换的合法性
 */
object WebSocketStateValidator {
    
    /**
     * 验证状态转换是否合法
     * @param fromState 原状态
     * @param toState 目标状态
     * @return 是否合法
     */
    fun isValidTransition(fromState: WebSocketState, toState: WebSocketState): Boolean {
        return when (fromState) {
            WebSocketState.DISCONNECTED -> {
                toState in setOf(WebSocketState.CONNECTING, WebSocketState.CLOSED)
            }
            WebSocketState.CONNECTING -> {
                toState in setOf(
                    WebSocketState.CONNECTED,
                    WebSocketState.ERROR,
                    WebSocketState.DISCONNECTED,
                    WebSocketState.CLOSED
                )
            }
            WebSocketState.CONNECTED -> {
                toState in setOf(
                    WebSocketState.CLOSING,
                    WebSocketState.ERROR,
                    WebSocketState.DISCONNECTED,
                    WebSocketState.RECONNECTING
                )
            }
            WebSocketState.RECONNECTING -> {
                toState in setOf(
                    WebSocketState.CONNECTED,
                    WebSocketState.ERROR,
                    WebSocketState.DISCONNECTED,
                    WebSocketState.CLOSED
                )
            }
            WebSocketState.CLOSING -> {
                toState in setOf(
                    WebSocketState.CLOSED,
                    WebSocketState.DISCONNECTED,
                    WebSocketState.ERROR
                )
            }
            WebSocketState.CLOSED -> {
                toState in setOf(
                    WebSocketState.CONNECTING,
                    WebSocketState.DISCONNECTED
                )
            }
            WebSocketState.ERROR -> {
                toState in setOf(
                    WebSocketState.CONNECTING,
                    WebSocketState.RECONNECTING,
                    WebSocketState.DISCONNECTED,
                    WebSocketState.CLOSED
                )
            }
        }
    }
    
    /**
     * 获取状态转换建议
     * @param fromState 原状态
     * @return 可能的目标状态列表
     */
    fun getValidTransitions(fromState: WebSocketState): Set<WebSocketState> {
        return WebSocketState.values().filter { toState ->
            isValidTransition(fromState, toState)
        }.toSet()
    }
}
