package com.yjsoft.roadtravel.basiclibrary.network

import android.content.Context
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.yjsoft.roadtravel.basiclibrary.logger.LogManager
import com.yjsoft.roadtravel.basiclibrary.network.cache.CacheInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.cache.NetworkCache
import com.yjsoft.roadtravel.basiclibrary.network.cache.NetworkCacheInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.config.NetworkConfig
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.AuthInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.CommonParamsInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.DefaultErrorHandler
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.ErrorInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.LoggingInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.LoginStatusHandler
import com.yjsoft.roadtravel.basiclibrary.network.interceptors.TokenProvider
import com.yjsoft.roadtravel.basiclibrary.network.proxy.ProxyManager
import com.yjsoft.roadtravel.basiclibrary.network.utils.RetryInterceptor
import com.yjsoft.roadtravel.basiclibrary.network.utils.RetryPolicy
import okhttp3.OkHttpClient
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory

/**
 * 优化的Retrofit实例管理器
 * 集成了认证、错误处理、缓存、重试等功能
 */
class RetrofitInstance private constructor(
    private val context: Context,
    private val tokenProvider: TokenProvider? = null
) {

    // Gson配置
    private val gsonInstance: Gson by lazy {
        GsonBuilder()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .serializeNulls()
            .create()
    }

    // 网络缓存管理器
    private val networkCacheInstance: NetworkCache by lazy {
        NetworkCache(context)
    }

    // 登录状态处理器
    private val loginStatusHandler: LoginStatusHandler by lazy {
        LoginStatusHandler(context)
    }

    // 核心拦截器（立即加载）
    private val coreInterceptors: List<okhttp3.Interceptor> by lazy {
        listOfNotNull(
            // 1. 公共参数拦截器（最先执行，为所有请求添加公共参数）
            CommonParamsInterceptor(context),
            // 2. 认证拦截器
            tokenProvider?.let { provider -> AuthInterceptor(context, provider) }
        )
    }

    // 扩展拦截器（延迟加载）
    private val extensionInterceptors: List<okhttp3.Interceptor> by lazy {
        listOf(
            // 3. 应用级拦截器
            CacheInterceptor(),
            // 4. 日志拦截器
            LoggingInterceptor.create(),
            // 5. 重试拦截器
            RetryInterceptor(RetryPolicy.default()),
            // 6. 错误处理拦截器
            ErrorInterceptor(gsonInstance, DefaultErrorHandler(), loginStatusHandler)
        )
    }

    // 网络级拦截器（延迟加载）
    private val networkInterceptors: List<okhttp3.Interceptor> by lazy {
        listOf(
            NetworkCacheInterceptor()
        )
    }

    // OkHttp客户端（渐进式初始化）
    private val okHttpClientInstance: OkHttpClient by lazy {
        createOkHttpClient(includeExtensions = true)
    }

    // 轻量级OkHttp客户端（仅核心拦截器）
    private val coreOkHttpClientInstance: OkHttpClient by lazy {
        createOkHttpClient(includeExtensions = false)
    }

    /**
     * 创建OkHttp客户端
     * @param includeExtensions 是否包含扩展拦截器
     */
    private fun createOkHttpClient(includeExtensions: Boolean): OkHttpClient {
        return OkHttpClient.Builder()
            .apply {
                // 超时配置
                connectTimeout(NetworkConfig.getConnectTimeout(), NetworkConfig.getTimeoutUnit())
                readTimeout(NetworkConfig.getReadTimeout(), NetworkConfig.getTimeoutUnit())
                writeTimeout(NetworkConfig.getWriteTimeout(), NetworkConfig.getTimeoutUnit())

                // 代理配置 - 强制直连，绕过系统代理
                if (NetworkConfig.isDirectConnectionEnabled()) {
                    proxy(ProxyManager.getDirectConnectionProxy())
                    // 记录代理状态（仅在调试模式下）
                    if (NetworkConfig.isDebugMode() && NetworkConfig.isProxyDetectionEnabled()) {
                        ProxyManager.logProxyStatus(context)
                    }
                }

                // 添加核心拦截器
                coreInterceptors.forEach { addInterceptor(it) }

                if (includeExtensions) {
                    // 缓存配置（仅在完整模式下）
                    cache(networkCacheInstance.getCache())

                    // 添加扩展拦截器
                    extensionInterceptors.forEach { addInterceptor(it) }

                    // 添加网络级拦截器
                    networkInterceptors.forEach { addNetworkInterceptor(it) }
                }
            }
            .build()
    }

    // Retrofit实例
    private val retrofitInstance: Retrofit by lazy {
        Retrofit.Builder()
            .baseUrl(NetworkConfig.getBaseUrl())
            .client(okHttpClientInstance)
            .addConverterFactory(GsonConverterFactory.create(gsonInstance))
            .build()
    }

    // API服务实例
    val api: ApiService by lazy {
        retrofitInstance.create(ApiService::class.java)
    }

    /**
     * 获取网络缓存管理器
     */
    fun getNetworkCache(): NetworkCache = networkCacheInstance

    /**
     * 获取Gson实例
     */
    fun getGson(): Gson = gsonInstance

    /**
     * 获取OkHttp客户端（完整版，包含所有拦截器）
     */
    fun getOkHttpClient(): OkHttpClient = okHttpClientInstance

    /**
     * 获取核心OkHttp客户端（轻量级，仅核心拦截器）
     */
    fun getCoreOkHttpClient(): OkHttpClient = coreOkHttpClientInstance

    /**
     * 获取Retrofit实例
     */
    fun getRetrofit(): Retrofit = retrofitInstance

    /**
     * 创建自定义API服务
     */
    fun <T> createService(serviceClass: Class<T>): T {
        return retrofitInstance.create(serviceClass)
    }

    /**
     * 预热网络组件
     * 提前初始化关键组件以减少首次请求延迟
     */
    fun warmUp() {
        try {
            // 触发懒加载组件的初始化
            okHttpClientInstance
            retrofitInstance
            api
        } catch (e: Exception) {
            // 预热失败不影响正常使用
            LogManager.w("RetrofitInstance", "预热失败: ${e.message}")
        }
    }

    /**
     * 轻量级预热（仅核心组件）
     */
    fun warmUpCore() {
        try {
            // 仅初始化核心组件
            coreOkHttpClientInstance
            gsonInstance
        } catch (e: Exception) {
            LogManager.w("RetrofitInstance", "核心预热失败: ${e.message}")
        }
    }

    /**
     * 检查是否已完全初始化
     */
    fun isFullyInitialized(): Boolean {
        return try {
            // 检查是否已触发完整初始化
            okHttpClientInstance.interceptors.size > coreInterceptors.size
        } catch (e: Exception) {
            false
        }
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        networkCacheInstance.close()
    }

    companion object {
        @Volatile
        private var INSTANCE: RetrofitInstance? = null

        /**
         * 获取单例实例
         */
        fun getInstance(
            context: Context,
            tokenProvider: TokenProvider? = null
        ): RetrofitInstance {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: RetrofitInstance(
                    context.applicationContext,
                    tokenProvider
                ).also { INSTANCE = it }
            }
        }

        /**
         * 重置实例（用于环境切换等场景）
         */
        fun resetInstance() {
            synchronized(this) {
                INSTANCE?.cleanup()
                INSTANCE = null
            }
        }
    }
}