package com.yjsoft.roadtravel.basiclibrary.network.websocket

import android.util.Log
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import okhttp3.*
import okio.ByteString
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

/**
 * WebSocket客户端核心类
 * 封装OkHttp WebSocket的核心功能，提供连接、发送、接收、断开等操作
 */
class WebSocketClient(
    private val config: WebSocketConfig,
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val TAG = "WebSocketClient"
    }
    
    // 状态管理
    private val stateManager = WebSocketStateManager()
    
    // WebSocket实例
    private var webSocket: WebSocket? = null
    
    // 协程作用域
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    // 重连相关
    private val retryCount = AtomicInteger(0)
    private var reconnectJob: Job? = null
    private val isReconnecting = AtomicBoolean(false)
    
    // 心跳相关
    private var heartbeatJob: Job? = null
    private val heartbeatFailureCount = AtomicInteger(0)
    
    // 消息流
    private val _messageFlow = MutableSharedFlow<String>(replay = 0)
    val messageFlow: SharedFlow<String> = _messageFlow.asSharedFlow()
    
    // 监听器
    private var listener: WebSocketListener? = null
    
    // 内部WebSocket监听器
    private val internalListener = object : okhttp3.WebSocketListener() {
        override fun onOpen(webSocket: WebSocket, response: Response) {
            log("WebSocket连接已打开")
            stateManager.updateState(WebSocketState.CONNECTED, "连接成功")
            retryCount.set(0)
            isReconnecting.set(false)
            
            // 启动心跳
            startHeartbeat()
            
            listener?.onOpen(webSocket, response)
        }
        
        override fun onMessage(webSocket: WebSocket, text: String) {
            log("收到文本消息: $text")
            
            // 处理心跳响应
            if (isHeartbeatResponse(text)) {
                handleHeartbeatResponse()
                return
            }
            
            // 发送到消息流
            scope.launch {
                _messageFlow.emit(text)
            }
            
            listener?.onMessage(webSocket, text)
        }
        
        override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
            log("收到二进制消息: ${bytes.size} bytes")
            listener?.onMessage(webSocket, bytes)
        }
        
        override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
            log("WebSocket正在关闭: code=$code, reason=$reason")
            stateManager.updateState(WebSocketState.CLOSING, "服务器请求关闭: $reason")
            listener?.onClosing(webSocket, code, reason)
        }
        
        override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
            log("WebSocket已关闭: code=$code, reason=$reason")
            stateManager.updateState(WebSocketState.CLOSED, "连接已关闭: $reason")
            cleanup()
            
            // 根据关闭代码决定是否重连
            if (shouldReconnectOnClose(code)) {
                scheduleReconnect()
            }
            
            listener?.onClosed(webSocket, code, reason)
        }
        
        override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
            log("WebSocket连接失败: ${t.message}")
            stateManager.updateState(WebSocketState.ERROR, "连接失败: ${t.message}")
            cleanup()
            
            // 如果启用了自动重连，则尝试重连
            if (config.enableAutoReconnect) {
                scheduleReconnect()
            }
            
            listener?.onFailure(webSocket, t, response)
        }
    }
    
    /**
     * 设置监听器
     */
    fun setListener(listener: WebSocketListener?) {
        this.listener = listener
    }
    
    /**
     * 获取当前状态
     */
    fun getCurrentState(): WebSocketState = stateManager.getCurrentState()
    
    /**
     * 获取状态流
     */
    fun getStateFlow() = stateManager.currentState
    
    /**
     * 连接WebSocket
     */
    fun connect() {
        if (stateManager.getCurrentState().isConnecting() || stateManager.getCurrentState().isConnected()) {
            log("WebSocket已连接或正在连接中")
            return
        }
        
        log("开始连接WebSocket: ${config.url}")
        stateManager.updateState(WebSocketState.CONNECTING, "开始连接")
        
        try {
            val request = buildRequest()
            webSocket = okHttpClient.newWebSocket(request, internalListener)
        } catch (e: Exception) {
            log("创建WebSocket连接失败: ${e.message}")
            stateManager.updateState(WebSocketState.ERROR, "创建连接失败: ${e.message}")
        }
    }
    
    /**
     * 发送文本消息
     */
    fun sendMessage(message: String): Boolean {
        val currentWebSocket = webSocket
        return if (currentWebSocket != null && stateManager.getCurrentState().canSendMessage()) {
            try {
                val success = currentWebSocket.send(message)
                if (success) {
                    log("发送消息成功: $message")
                } else {
                    log("发送消息失败: 队列已满")
                }
                success
            } catch (e: Exception) {
                log("发送消息异常: ${e.message}")
                false
            }
        } else {
            log("无法发送消息: WebSocket未连接")
            false
        }
    }
    
    /**
     * 发送二进制消息
     */
    fun sendMessage(bytes: ByteString): Boolean {
        val currentWebSocket = webSocket
        return if (currentWebSocket != null && stateManager.getCurrentState().canSendMessage()) {
            try {
                val success = currentWebSocket.send(bytes)
                if (success) {
                    log("发送二进制消息成功: ${bytes.size} bytes")
                } else {
                    log("发送二进制消息失败: 队列已满")
                }
                success
            } catch (e: Exception) {
                log("发送二进制消息异常: ${e.message}")
                false
            }
        } else {
            log("无法发送二进制消息: WebSocket未连接")
            false
        }
    }
    
    /**
     * 关闭连接
     */
    fun close(code: Int = WebSocketCloseCodes.NORMAL_CLOSURE, reason: String = "正常关闭") {
        log("关闭WebSocket连接: code=$code, reason=$reason")
        
        // 停止重连
        stopReconnect()
        
        // 关闭WebSocket
        webSocket?.close(code, reason)
        
        // 清理资源
        cleanup()
        
        stateManager.updateState(WebSocketState.DISCONNECTED, "主动关闭连接")
    }
    
    /**
     * 销毁客户端
     */
    fun destroy() {
        log("销毁WebSocket客户端")
        close()
        scope.cancel()
        stateManager.reset()
    }
    
    /**
     * 构建WebSocket请求
     */
    private fun buildRequest(): Request {
        val builder = Request.Builder().url(config.url)
        
        // 添加自定义请求头
        config.headers.forEach { (key, value) ->
            builder.header(key, value)
        }
        
        return builder.build()
    }
    
    /**
     * 启动心跳
     */
    private fun startHeartbeat() {
        if (!config.heartbeatConfig.enabled || !config.heartbeatConfig.validate()) {
            return
        }
        
        stopHeartbeat()
        
        heartbeatJob = scope.launch {
            while (isActive && stateManager.getCurrentState().isConnected()) {
                delay(config.heartbeatConfig.interval)
                
                if (stateManager.getCurrentState().isConnected()) {
                    val success = sendMessage(config.heartbeatConfig.message)
                    if (!success) {
                        val failureCount = heartbeatFailureCount.incrementAndGet()
                        log("心跳发送失败，失败次数: $failureCount")
                        
                        if (failureCount >= config.heartbeatConfig.maxFailureCount) {
                            log("心跳失败次数超过限制，关闭连接")
                            close(WebSocketCloseCodes.ABNORMAL_CLOSURE, "心跳失败")
                            break
                        }
                    }
                }
            }
        }
    }
    
    /**
     * 停止心跳
     */
    private fun stopHeartbeat() {
        heartbeatJob?.cancel()
        heartbeatJob = null
        heartbeatFailureCount.set(0)
    }
    
    /**
     * 判断是否为心跳响应
     */
    private fun isHeartbeatResponse(message: String): Boolean {
        return message == "pong" || message == config.heartbeatConfig.message
    }
    
    /**
     * 处理心跳响应
     */
    private fun handleHeartbeatResponse() {
        heartbeatFailureCount.set(0)
        log("收到心跳响应")
    }
    
    /**
     * 判断是否应该在关闭时重连
     */
    private fun shouldReconnectOnClose(code: Int): Boolean {
        return config.enableAutoReconnect && code != WebSocketCloseCodes.NORMAL_CLOSURE
    }
    
    /**
     * 安排重连
     */
    private fun scheduleReconnect() {
        if (!config.enableAutoReconnect || isReconnecting.get()) {
            return
        }
        
        val currentRetryCount = retryCount.get()
        if (!config.reconnectConfig.shouldRetry(currentRetryCount)) {
            log("重连次数已达上限，停止重连")
            stateManager.updateState(WebSocketState.DISCONNECTED, "重连次数超限")
            return
        }
        
        isReconnecting.set(true)
        stateManager.updateState(WebSocketState.RECONNECTING, "准备重连")
        
        val delay = config.reconnectConfig.calculateDelay(currentRetryCount)
        log("安排重连，延迟: ${delay}ms，重连次数: ${currentRetryCount + 1}")
        
        reconnectJob = scope.launch {
            delay(delay)
            
            if (isActive && isReconnecting.get()) {
                retryCount.incrementAndGet()
                log("开始第${retryCount.get()}次重连")
                connect()
            }
        }
    }
    
    /**
     * 停止重连
     */
    private fun stopReconnect() {
        isReconnecting.set(false)
        reconnectJob?.cancel()
        reconnectJob = null
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        stopHeartbeat()
        stopReconnect()
        webSocket = null
    }
    
    /**
     * 手动重连
     */
    fun reconnect() {
        log("手动重连WebSocket")
        close(WebSocketCloseCodes.NORMAL_CLOSURE, "手动重连")

        // 重置重连计数
        retryCount.set(0)

        // 延迟一小段时间后重连
        scope.launch {
            delay(1000)
            connect()
        }
    }

    /**
     * 检查连接状态
     */
    fun isConnected(): Boolean = stateManager.getCurrentState().isConnected()

    /**
     * 检查是否正在连接
     */
    fun isConnecting(): Boolean = stateManager.getCurrentState().isConnecting()

    /**
     * 获取重连次数
     */
    fun getRetryCount(): Int = retryCount.get()

    /**
     * 添加状态监听器
     */
    fun addStateListener(listener: WebSocketStateListener) {
        stateManager.addStateListener(listener)
    }

    /**
     * 移除状态监听器
     */
    fun removeStateListener(listener: WebSocketStateListener) {
        stateManager.removeStateListener(listener)
    }

    /**
     * 日志输出
     */
    private fun log(message: String) {
        if (config.enableLogging) {
            Log.d(TAG, message)
        }
    }
}
