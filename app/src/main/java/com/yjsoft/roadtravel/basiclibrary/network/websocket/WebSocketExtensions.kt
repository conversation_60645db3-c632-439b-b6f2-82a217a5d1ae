package com.yjsoft.roadtravel.basiclibrary.network.websocket

import kotlinx.coroutines.*
import kotlinx.coroutines.flow.*
import kotlin.coroutines.CoroutineContext

/**
 * WebSocket扩展函数
 * 提供便捷的扩展函数，简化WebSocket使用
 */

/**
 * WebSocketClient扩展：协程方式连接
 */
suspend fun WebSocketClient.connectSuspend(): Boolean = withContext(Dispatchers.IO) {
    val deferred = CompletableDeferred<Boolean>()
    
    val stateListener = object : WebSocketStateListener {
        override fun onStateChanged(oldState: WebSocketState, newState: WebSocketState) {
            when (newState) {
                WebSocketState.CONNECTED -> {
                    removeStateListener(this)
                    deferred.complete(true)
                }
                WebSocketState.ERROR, WebSocketState.CLOSED -> {
                    removeStateListener(this)
                    deferred.complete(false)
                }
                else -> { /* 继续等待 */ }
            }
        }
    }
    
    addStateListener(stateListener)
    connect()
    
    try {
        withTimeout(30000) { // 30秒超时
            deferred.await()
        }
    } catch (e: TimeoutCancellationException) {
        removeStateListener(stateListener)
        false
    }
}

/**
 * WebSocketClient扩展：协程方式发送消息并等待响应
 */
suspend fun WebSocketClient.sendAndWaitForResponse(
    message: String,
    timeout: Long = 10000L,
    responseFilter: (String) -> Boolean = { true }
): String? = withContext(Dispatchers.IO) {
    val deferred = CompletableDeferred<String?>()
    
    // 监听消息流
    val job = messageFlow
        .filter(responseFilter)
        .take(1)
        .onEach { response ->
            deferred.complete(response)
        }
        .launchIn(this)
    
    // 发送消息
    val sent = sendMessage(message)
    if (!sent) {
        job.cancel()
        deferred.complete(null)
    }
    
    try {
        withTimeout(timeout) {
            deferred.await()
        }
    } catch (e: TimeoutCancellationException) {
        job.cancel()
        null
    }
}

/**
 * WebSocketClient扩展：获取状态变化流
 */
fun WebSocketClient.stateChanges(): Flow<WebSocketState> {
    return getStateFlow()
}

/**
 * WebSocketClient扩展：等待特定状态
 */
suspend fun WebSocketClient.waitForState(
    targetState: WebSocketState,
    timeout: Long = 30000L
): Boolean = withContext(Dispatchers.IO) {
    if (getCurrentState() == targetState) {
        return@withContext true
    }
    
    try {
        withTimeout(timeout) {
            stateChanges()
                .first { it == targetState }
            true
        }
    } catch (e: TimeoutCancellationException) {
        false
    }
}

/**
 * WebSocketClient扩展：安全发送消息（带重试）
 */
suspend fun WebSocketClient.sendMessageSafely(
    message: String,
    maxRetries: Int = 3,
    retryDelay: Long = 1000L
): Boolean = withContext(Dispatchers.IO) {
    repeat(maxRetries) { attempt ->
        if (getCurrentState().canSendMessage()) {
            val success = sendMessage(message)
            if (success) {
                return@withContext true
            }
        }
        
        if (attempt < maxRetries - 1) {
            delay(retryDelay)
        }
    }
    false
}

/**
 * WebSocketManager扩展：创建并连接客户端
 */
suspend fun WebSocketManager.createAndConnect(
    key: String,
    config: WebSocketConfig
): WebSocketClient? = withContext(Dispatchers.IO) {
    val client = createClient(key, config)
    val connected = client.connectSuspend()
    if (connected) client else null
}

/**
 * WebSocketManager扩展：批量发送消息
 */
suspend fun WebSocketManager.broadcastMessage(
    message: String,
    excludeKeys: Set<String> = emptySet()
): Map<String, Boolean> = withContext(Dispatchers.IO) {
    val results = mutableMapOf<String, Boolean>()
    
    getAllClients().forEach { (key, client) ->
        if (key !in excludeKeys) {
            results[key] = client.sendMessage(message)
        }
    }
    
    results
}

// WebSocketConfig已经有内置的copy方法，不需要扩展函数

/**
 * Flow扩展：过滤JSON消息
 */
fun Flow<String>.filterJsonMessages(): Flow<String> {
    return filter { WebSocketUtils.isJsonMessage(it) }
}

/**
 * Flow扩展：解析JSON消息
 */
inline fun <reified T> Flow<String>.parseJsonMessages(): Flow<T> {
    return mapNotNull { message ->
        WebSocketUtils.parseJsonMessage(message, T::class.java)
    }
}

/**
 * Flow扩展：过滤消息类型
 */
fun Flow<String>.filterMessageType(type: String): Flow<String> {
    return mapNotNull { message ->
        val wrapper = WebSocketUtils.parseMessageWrapper(message)
        if (wrapper?.type == type) message else null
    }
}

/**
 * Flow扩展：提取消息数据
 */
inline fun <reified T> Flow<String>.extractMessageData(): Flow<T> {
    return mapNotNull { message ->
        val wrapper = WebSocketUtils.parseMessageWrapper(message)
        wrapper?.data as? T
    }
}

/**
 * Flow扩展：添加超时
 */
fun <T> Flow<T>.timeoutAfter(timeoutMs: Long): Flow<T> {
    return flow {
        withTimeout(timeoutMs) {
            collect { emit(it) }
        }
    }
}

/**
 * Flow扩展：重试机制
 */
fun <T> Flow<T>.retryWithDelay(
    maxRetries: Int,
    delayMs: Long = 1000L,
    backoffMultiplier: Double = 2.0
): Flow<T> {
    return retryWhen { _, attempt ->
        if (attempt < maxRetries) {
            val delay = (delayMs * Math.pow(backoffMultiplier, attempt.toDouble())).toLong()
            delay(delay)
            true
        } else {
            false
        }
    }
}

/**
 * 协程扩展：安全启动WebSocket操作
 */
fun CoroutineScope.launchWebSocketOperation(
    context: CoroutineContext = Dispatchers.IO,
    start: CoroutineStart = CoroutineStart.DEFAULT,
    block: suspend CoroutineScope.() -> Unit
): Job {
    return launch(context + SupervisorJob(), start) {
        try {
            block()
        } catch (e: CancellationException) {
            // 协程被取消，正常情况
            throw e
        } catch (e: Exception) {
            // 记录其他异常但不传播
            android.util.Log.e("WebSocketExtensions", "WebSocket操作异常", e)
        }
    }
}

/**
 * 简化的WebSocket客户端构建器
 */
class WebSocketClientBuilder {
    private var url: String = ""
    private var enableLogging: Boolean = true
    private var enableAutoReconnect: Boolean = true
    private var heartbeatInterval: Long = 30000L
    private var maxRetries: Int = 3
    private val headers = mutableMapOf<String, String>()
    
    fun url(url: String) = apply { this.url = url }
    fun enableLogging(enable: Boolean) = apply { this.enableLogging = enable }
    fun enableAutoReconnect(enable: Boolean) = apply { this.enableAutoReconnect = enable }
    fun heartbeatInterval(interval: Long) = apply { this.heartbeatInterval = interval }
    fun maxRetries(retries: Int) = apply { this.maxRetries = retries }
    fun addHeader(key: String, value: String) = apply { this.headers[key] = value }
    
    fun build(): WebSocketConfig {
        require(url.isNotEmpty()) { "WebSocket URL不能为空" }
        require(WebSocketUtils.isValidWebSocketUrl(url)) { "WebSocket URL格式无效: $url" }
        
        return WebSocketConfig(
            url = url,
            enableLogging = enableLogging,
            enableAutoReconnect = enableAutoReconnect,
            heartbeatConfig = HeartbeatConfig(
                enabled = true,
                interval = heartbeatInterval
            ),
            reconnectConfig = ReconnectConfig(
                maxRetryCount = maxRetries
            ),
            headers = headers
        )
    }
}

/**
 * DSL风格的WebSocket配置构建器
 */
fun webSocketConfig(block: WebSocketClientBuilder.() -> Unit): WebSocketConfig {
    return WebSocketClientBuilder().apply(block).build()
}
