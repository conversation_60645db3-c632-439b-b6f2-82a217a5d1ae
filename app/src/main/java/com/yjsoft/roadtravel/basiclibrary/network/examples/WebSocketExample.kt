package com.yjsoft.roadtravel.basiclibrary.network.examples

import android.content.Context
import android.util.Log
import com.yjsoft.roadtravel.basiclibrary.network.NetworkManager
import com.yjsoft.roadtravel.basiclibrary.network.websocket.*
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.onEach

/**
 * WebSocket使用示例
 * 提供完整的使用示例，帮助开发者快速上手
 */
class WebSocketExample(private val context: Context) {
    
    companion object {
        private const val TAG = "WebSocketExample"
        private const val WS_URL = "wss://echo.websocket.org" // 测试用的WebSocket服务器
        private const val AI_CHAT_CLIENT_KEY = "ai_chat"
    }
    
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private lateinit var webSocketManager: WebSocketManager
    
    /**
     * 初始化WebSocket管理器
     */
    fun initialize() {
        webSocketManager = NetworkManager.getWebSocketManager(context)
        Log.d(TAG, "WebSocket管理器初始化完成")
    }
    
    /**
     * 基础用法示例
     */
    fun basicUsageExample() {
        scope.launch {
            try {
                // 1. 创建配置
                val config = WebSocketConfig.development(WS_URL)
                
                // 2. 创建客户端
                val client = webSocketManager.createClient("basic_client", config)
                
                // 3. 设置监听器
                client.setListener(object : SimpleWebSocketListenerAdapter(object : SimpleWebSocketListener {
                    override fun onConnected() {
                        Log.d(TAG, "基础示例：连接成功")
                    }
                    
                    override fun onMessageReceived(message: String) {
                        Log.d(TAG, "基础示例：收到消息 - $message")
                    }
                    
                    override fun onDisconnected(code: Int, reason: String) {
                        Log.d(TAG, "基础示例：连接断开 - $code: $reason")
                    }
                    
                    override fun onError(error: Throwable) {
                        Log.e(TAG, "基础示例：连接错误", error)
                    }
                }) {})
                
                // 4. 连接
                client.connect()
                
                // 5. 等待连接成功
                if (client.waitForState(WebSocketState.CONNECTED)) {
                    Log.d(TAG, "基础示例：连接建立成功")
                    
                    // 6. 发送消息
                    client.sendMessage("Hello WebSocket!")
                    
                    // 7. 延迟后断开
                    delay(5000)
                    client.close()
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "基础示例异常", e)
            }
        }
    }
    
    /**
     * 高级用法示例：AI聊天场景
     */
    fun aiChatExample() {
        scope.launch {
            try {
                // 1. 使用DSL构建配置
                val config = webSocketConfig {
                    url("wss://api.example.com/ai-chat")
                    enableLogging(true)
                    enableAutoReconnect(true)
                    heartbeatInterval(30000L)
                    maxRetries(5)
                    addHeader("Authorization", "Bearer your-token")
                    addHeader("User-Agent", "RoadTravel-App/1.0")
                }
                
                // 2. 创建并连接客户端
                val client = webSocketManager.createAndConnect(AI_CHAT_CLIENT_KEY, config)
                
                if (client != null) {
                    Log.d(TAG, "AI聊天示例：连接成功")
                    
                    // 3. 监听消息流
                    client.messageFlow
                        .filterJsonMessages()
                        .parseJsonMessages<AIChatMessage>()
                        .onEach { message ->
                            handleAIChatMessage(message)
                        }
                        .collect()
                        
                } else {
                    Log.e(TAG, "AI聊天示例：连接失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "AI聊天示例异常", e)
            }
        }
    }
    
    /**
     * 协程扩展用法示例
     */
    fun coroutineExtensionExample() {
        scope.launch {
            try {
                val config = WebSocketConfig.default(WS_URL)
                val client = webSocketManager.createClient("coroutine_client", config)
                
                // 1. 协程方式连接
                val connected = client.connectSuspend()
                if (connected) {
                    Log.d(TAG, "协程示例：连接成功")
                    
                    // 2. 安全发送消息（带重试）
                    val sent = client.sendMessageSafely("Hello from coroutine!", maxRetries = 3)
                    Log.d(TAG, "协程示例：消息发送${if (sent) "成功" else "失败"}")
                    
                    // 3. 发送消息并等待响应
                    val response = client.sendAndWaitForResponse(
                        message = "ping",
                        timeout = 5000L,
                        responseFilter = { it.contains("pong") }
                    )
                    Log.d(TAG, "协程示例：收到响应 - $response")
                    
                    // 4. 监听状态变化
                    launch {
                        client.stateChanges()
                            .onEach { state ->
                                Log.d(TAG, "协程示例：状态变化 - $state")
                            }
                            .collect()
                    }
                    
                } else {
                    Log.e(TAG, "协程示例：连接失败")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "协程示例异常", e)
            }
        }
    }
    
    /**
     * 批量操作示例
     */
    fun batchOperationExample() {
        scope.launch {
            try {
                // 1. 创建多个客户端
                val configs = listOf(
                    "client1" to WebSocketConfig.default("$WS_URL/1"),
                    "client2" to WebSocketConfig.default("$WS_URL/2"),
                    "client3" to WebSocketConfig.default("$WS_URL/3")
                )
                
                configs.forEach { (key, config) ->
                    webSocketManager.createClient(key, config)
                    webSocketManager.connect(key)
                }
                
                // 2. 等待所有连接建立
                delay(3000)
                
                // 3. 批量发送消息
                val results = webSocketManager.broadcastMessage("Hello from all clients!")
                results.forEach { (key, success) ->
                    Log.d(TAG, "批量示例：客户端 $key 发送${if (success) "成功" else "失败"}")
                }
                
                // 4. 获取所有客户端状态
                webSocketManager.getAllClients().forEach { (key, client) ->
                    Log.d(TAG, "批量示例：客户端 $key 状态 - ${client.getCurrentState()}")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "批量示例异常", e)
            }
        }
    }
    
    /**
     * 错误处理和重连示例
     */
    fun errorHandlingExample() {
        scope.launch {
            try {
                val config = WebSocketConfig(
                    url = "wss://invalid-url-for-testing.com",
                    enableAutoReconnect = true,
                    reconnectConfig = ReconnectConfig(
                        maxRetryCount = 3,
                        initialDelay = 1000L,
                        maxDelay = 10000L
                    )
                )
                
                val client = webSocketManager.createClient("error_client", config)
                
                // 添加状态监听器
                client.addStateListener(object : WebSocketStateListener {
                    override fun onStateChanged(oldState: WebSocketState, newState: WebSocketState) {
                        Log.d(TAG, "错误处理示例：状态变化 $oldState -> $newState")
                        
                        when (newState) {
                            WebSocketState.ERROR -> {
                                Log.w(TAG, "错误处理示例：连接出错，重试次数：${client.getRetryCount()}")
                            }
                            WebSocketState.RECONNECTING -> {
                                Log.i(TAG, "错误处理示例：正在重连...")
                            }
                            else -> { /* 其他状态 */ }
                        }
                    }
                })
                
                // 尝试连接
                client.connect()
                
                // 等待一段时间观察重连行为
                delay(30000)
                
            } catch (e: Exception) {
                Log.e(TAG, "错误处理示例异常", e)
            }
        }
    }
    
    /**
     * 处理AI聊天消息
     */
    private fun handleAIChatMessage(message: AIChatMessage) {
        when (message.type) {
            "chat_response" -> {
                Log.d(TAG, "AI回复：${message.content}")
                // 处理AI回复
            }
            "typing_indicator" -> {
                Log.d(TAG, "AI正在输入...")
                // 显示输入指示器
            }
            "error" -> {
                Log.e(TAG, "AI聊天错误：${message.content}")
                // 处理错误
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        scope.cancel()
        webSocketManager.destroyAll()
        Log.d(TAG, "WebSocket示例清理完成")
    }
}

/**
 * AI聊天消息数据类
 */
data class AIChatMessage(
    val type: String,
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
    val messageId: String? = null
)

/**
 * 在Activity中的使用示例
 */
/*
class MainActivity : AppCompatActivity() {
    private lateinit var webSocketExample: WebSocketExample
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 初始化WebSocket示例
        webSocketExample = WebSocketExample(this)
        webSocketExample.initialize()
        
        // 运行基础示例
        webSocketExample.basicUsageExample()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webSocketExample.cleanup()
    }
}
*/
