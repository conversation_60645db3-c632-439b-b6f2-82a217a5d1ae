[versions]
androidApplication = "8.12.0"
kotlinAndroid = "2.2.0"
kotlinCompose = "2.2.0"
activityCompose = "1.10.1"
composeBom = "2025.07.00"
coreKtx = "1.16.0"
espressoCore = "3.7.0"
junit = "4.13.2"
junitVersion = "1.3.0"
lifecycleRuntimeKtx = "2.9.2"
location = "latest.integration"
material3 = "1.3.2"
material-icons-extended ="1.7.8"
compose-foundation = "1.8.3"
ui = "1.8.3"
uiGraphics = "1.8.3"
uiTooling = "1.8.3"
uiToolingPreview = "1.8.3"
uiTestJunit4 = "1.8.3"
uiTestManifest = "1.8.3"
retrofit = "3.0.0"
okhttp = "5.1.0"
okhttp-logging-interceptor = "5.1.0"
gson-converter = "3.0.0"
gson = "2.13.1"
coroutines = "1.10.2"
lifecycle-viewmodel = "2.9.2"
timber = "5.0.1"
leakcanary = "2.14"
datastore = "1.1.7"
coil = "3.3.0"
# Navigation Compose
navigation-compose = "2.9.3"
# 序列化
kotlinx-serialization = "1.9.0"
# Hilt 依赖注入
hilt = "2.57"
hilt-navigation-compose = "1.2.0"
# SplashScreen API
splashscreen = "1.2.0-rc01"
# androidx.startup版本
startup = "1.2.0"
# 支付SDK版本
alipay-sdk = "15.8.35"
wechat-sdk = "6.8.34"
unionpay-sdk = "3.5.12"
# WebSocket版本 (使用OkHttp内置WebSocket支持)
okhttp-websocket = "5.1.0"

[libraries]
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "composeBom" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtx" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCore" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "junitVersion" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycle-viewmodel" }
androidx-material3 = { module = "androidx.compose.material3:material3", version.ref = "material3" }
androidx-material-icons-extended = { module = "androidx.compose.material:material-icons-extended", version.ref = "material-icons-extended" }
androidx-compose-foundation = { module = "androidx.compose.foundation:foundation", version.ref = "compose-foundation" }
androidx-ui = { module = "androidx.compose.ui:ui", version.ref = "ui" }
androidx-ui-graphics = { module = "androidx.compose.ui:ui-graphics", version.ref = "uiGraphics" }
androidx-ui-tooling = { module = "androidx.compose.ui:ui-tooling", version.ref = "uiTooling" }
androidx-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview", version.ref = "uiToolingPreview" }
androidx-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4", version.ref = "uiTestJunit4" }
androidx-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest", version.ref = "uiTestManifest" }
junit = { module = "junit:junit", version.ref = "junit" }
# 高德 3D地图带有定位和搜索
map3d = { module = "com.amap.api:3dmap-location-search", version.ref = "location" }
retrofit-core = { group = "com.squareup.retrofit2", name = "retrofit", version.ref = "retrofit" }
retrofit-converter-gson = { group = "com.squareup.retrofit2", name = "converter-gson", version.ref = "gson-converter" }
okhttp-bom = { group = "com.squareup.okhttp3", name = "okhttp-bom", version.ref = "okhttp" }
okhttp-logging-interceptor = { group = "com.squareup.okhttp3", name = "logging-interceptor", version.ref = "okhttp-logging-interceptor" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
kotlinx-coroutines-core = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-core", version.ref = "coroutines" }
kotlinx-coroutines-android = { group = "org.jetbrains.kotlinx", name = "kotlinx-coroutines-android", version.ref = "coroutines" }
androidx-lifecycle-viewmodel-ktx = { group = "androidx.lifecycle", name = "lifecycle-viewmodel-ktx", version.ref = "lifecycle-viewmodel" }
timber = { group = "com.jakewharton.timber", name = "timber", version.ref = "timber" }
leakcanary-android = { group = "com.squareup.leakcanary", name = "leakcanary-android", version.ref = "leakcanary" }
androidx-datastore-preferences = { group = "androidx.datastore", name = "datastore-preferences", version.ref = "datastore" }
androidx-datastore-core = { group = "androidx.datastore", name = "datastore-core", version.ref = "datastore" }
coil-compose = { group = "io.coil-kt.coil3", name = "coil-compose", version.ref = "coil" }
coil-network-okhttp = { group = "io.coil-kt.coil3", name = "coil-network-okhttp", version.ref = "coil" }
# Navigation Compose
androidx-navigation-compose = { group = "androidx.navigation", name = "navigation-compose", version.ref = "navigation-compose" }
androidx-navigation-runtime-ktx = { group = "androidx.navigation", name = "navigation-runtime-ktx", version.ref = "navigation-compose" }
# 序列化
kotlinx-serialization-json = { group = "org.jetbrains.kotlinx", name = "kotlinx-serialization-json", version.ref = "kotlinx-serialization" }
# Hilt 依赖注入
hilt-android = { group = "com.google.dagger", name = "hilt-android", version.ref = "hilt" }
hilt-compiler = { group = "com.google.dagger", name = "hilt-compiler", version.ref = "hilt" }
hilt-navigation-compose = { group = "androidx.hilt", name = "hilt-navigation-compose", version.ref = "hilt-navigation-compose" }
# SplashScreen API
androidx-core-splashscreen = { group = "androidx.core", name = "core-splashscreen", version.ref = "splashscreen" }
# androidx.startup
androidx-startup-runtime = { group = "androidx.startup", name = "startup-runtime", version.ref = "startup" }
# 支付SDK依赖
alipay-sdk = { group = "com.alipay.sdk", name = "alipaysdk-android", version.ref = "alipay-sdk" }
wechat-sdk = { group = "com.tencent.mm.opensdk", name = "wechat-sdk-android", version.ref = "wechat-sdk" }
# WebSocket依赖 (OkHttp内置支持)
okhttp-websocket = { group = "com.squareup.okhttp3", name = "okhttp", version.ref = "okhttp-websocket" }

[plugins]
android-application = { id = "com.android.application", version.ref = "androidApplication" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlinAndroid" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlinCompose" }
kotlinx-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlinAndroid" }
hilt = { id = "com.google.dagger.hilt.android", version.ref = "hilt" }

[bundles]
